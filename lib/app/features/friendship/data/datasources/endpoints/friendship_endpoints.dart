import 'package:quycky/core/utils/server_url.dart';

class FriendshipEndpoints {
  static String profileByFriendUserId(int id) => ServerUrl.getConfiguredUrl(
      "users/getProfileByFriendUserId?friend_id=$id");
  static String inviteUser() =>
      ServerUrl.getConfiguredUrl("friendlyInvitations");
  static String orderInvitation() =>
      ServerUrl.getConfiguredUrl("friendlyInvitations/orderInvitation");
  static String acceptInvitation(int id) =>
      ServerUrl.getConfiguredUrl("friendlyInvitations/setAccepted?id=$id");
  static String rejectInvitation(int id) =>
      ServerUrl.getConfiguredUrl("friendlyInvitations/setRejected?id=$id");
  static String byFriendshipId(int id) =>
      ServerUrl.getConfiguredUrl("friendlyInvitations/id?id=$id");
  static String byUserId(int id) =>
      ServerUrl.getConfiguredUrl("friendlyInvitations/byUserId?id=$id");
  static String byInvitedUserId(int id) =>
      ServerUrl.getConfiguredUrl("friendlyInvitations/byInvitedUserId?id=$id");
  static String invitationsByInvitedUserIdAndUserId(
          int userId, int invitedUserId) =>
      ServerUrl.getConfiguredUrl(
          "friendlyInvitations/invitationsByInvitedUserIdByUserId?user_id=$userId&invited_user_id=$invitedUserId");
  static String allByUserId(int id) =>
      ServerUrl.getConfiguredUrl("friends/allByUserId?user_id=$id");
  static String checkFriendMatch(String id, bool isNewFriend) =>
      ServerUrl.getConfiguredUrl(
          "users/checkMatchByFriendId?friend_id=$id&new_friend=$isNewFriend");
  static String friendMatchHistory({int page = 1, int take = 10}) =>
      ServerUrl.getConfiguredUrl(
          "matchHistory/allByUserId?page=$page&take=$take");
  static String removeFriend(int friendId) =>
      ServerUrl.getConfiguredUrl("friends?friend_id=$friendId");
  static String generateProfileTempCode() =>
      ServerUrl.getConfiguredUrl("users/generateQRCodeForVibeCheck");
  static String validateProfileTempCode(String code) =>
      ServerUrl.getConfiguredUrl("users/validateQRCodeForVibeCheck?code=$code");
  static String generateFriendlyInvitationTempCode() =>
      ServerUrl.getConfiguredUrl(
          "friendlyInvitations/generateInvitationTicket");
  static String validateFriendlyInvitationTempCode(String code) =>
      ServerUrl.getConfiguredUrl(
          "friendlyInvitations/validateInvitationTicket?code=$code");
  static String blockPlayer() => ServerUrl.getConfiguredUrl("blacklists");
  static String reportPlayer() => ServerUrl.getConfiguredUrl("reports");
  static String getBlockedPlayersList(int userId) =>
      ServerUrl.getConfiguredUrl("blacklists/allByUserId?user_id=$userId");
}
