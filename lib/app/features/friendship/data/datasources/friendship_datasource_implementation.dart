import 'package:quycky/app/features/friendship/data/datasources/endpoints/friendship_endpoints.dart';
import 'package:quycky/app/features/friendship/data/datasources/friendship_datasource.dart';
import 'package:quycky/app/features/friendship/domain/entities/blocked_player_entity.dart';
import 'package:quycky/app/features/friendship/data/models/friend_match_model.dart';
import 'package:quycky/app/features/friendship/data/models/friendship_invite_model.dart';
import 'package:quycky/app/features/friendship/data/models/friendship_model.dart';
import 'package:quycky/app/features/friendship/data/models/friend_profile_model.dart';
import 'package:quycky/app/features/friendship/domain/entities/block_player_params_entity.dart'; // Adicionado
import 'package:quycky/app/features/friendship/domain/entities/report_player_params_entity.dart'; // Adicionado
import 'package:quycky/app/features/friendship/domain/request_entities/invite_request.dart';
import 'package:quycky/core/http_client/http_client.dart';
import 'package:quycky/core/usecase/errors/exceptions.dart';
import 'package:quycky/core/usecase/errors/failure.dart';

class FriendshipDatasourceImplementation implements IFriendshipDatasource {
  final HttpClient client;

  FriendshipDatasourceImplementation(this.client);

  @override
  Future<FriendshipInviteModel> acceptInvite(int id) async {
    final response = await client.put(FriendshipEndpoints.acceptInvitation(id));
    if (response.statusCode == 200) {
      return FriendshipInviteModel.fromJson(response.data);
    }
    throw ServerException();
  }

  @override
  Future<FriendProfileModel> getFriendProfile(int id) async {
    final response =
        await client.get(FriendshipEndpoints.profileByFriendUserId(id));
    if (response.statusCode == 200) {
      return FriendProfileModel.fromJson(response.data);
    }
    throw ServerException();
  }

  @override
  Future<List<FriendshipInviteModel>> getFriendshipInvitations(int id) async {
    final response = await client.get(FriendshipEndpoints.byUserId(id));
    if (response.statusCode == 200) {
      return (response.data as List<dynamic>)
          .map((x) => FriendshipInviteModel.fromJson(x as Map<String, dynamic>))
          .toList();
      // List<FriendshipInviteModel>.from((response.data as List<dynamic>)
      //     .map((x) => FriendshipInviteModel.fromJson(x)));
    }
    throw ServerException();
  }

  @override
  Future<List<FriendshipInviteModel>> getFriendshipInvitationsReceived(
      int id) async {
    final response = await client.get(FriendshipEndpoints.byInvitedUserId(id));
    if (response.statusCode == 200) {
      return List<FriendshipInviteModel>.from((response.data as List<dynamic>)
          .map((x) => FriendshipInviteModel.fromJson(x)));
    }
    throw ServerException();
  }

  @override
  Future<List<FriendshipModel>> getFriendships(int id) async {
    final response = await client.get(FriendshipEndpoints.allByUserId(id));
    if (response.statusCode == 200) {
      try {
        return List<FriendshipModel>.from((response.data as List<dynamic>)
            .map((x) => FriendshipModel.fromJson(x)));
      } catch (e) {
        // print('err==>$e');
      }
    }
    throw ServerException();
  }

  @override
  Future<FriendshipInviteModel> inviteUser(InviteRequest data) async {
    final response = await client.post(FriendshipEndpoints.inviteUser(),
        body: data.toJson());
    if (response.statusCode == 200) {
      return FriendshipInviteModel.fromJson(response.data);
    }
    throw ServerException();
  }

  @override
  Future<FriendshipInviteModel> orderInvitation(InviteRequest data) async {
    final response = await client.post(FriendshipEndpoints.orderInvitation(),
        body: data.toJson());
    if (response.statusCode == 200) {
      return FriendshipInviteModel.fromJson(response.data);
    }
    throw ServerException();
  }

  @override
  Future<FriendshipInviteModel> rejectInvite(int id) async {
    final response = await client.put(FriendshipEndpoints.rejectInvitation(id));
    if (response.statusCode == 200) {
      return FriendshipInviteModel.fromJson(response.data);
    }
    throw ServerException();
  }

  @override
  Future<FriendMatchModel> checkFriendMatch(String id, bool isNewFriend) async {
    print(
        'Glória a Deus!!!\\o/==>${FriendshipEndpoints.checkFriendMatch(id, isNewFriend)}');
    final response =
        await client.get(FriendshipEndpoints.checkFriendMatch(id, isNewFriend));

    if (response.statusCode == 200) {
      return FriendMatchModel.fromJson(response.data);
    }
    throw ServerException();
  }

  @override
  Future<List<FriendMatchModel>> getFriendMatchHistory() async {
    final response = await client
        .get(FriendshipEndpoints.friendMatchHistory(page: 1, take: 10));
    if (response.statusCode == 200) {
      return (response.data as List<dynamic>)
          .map((x) => FriendMatchModel.fromJson(x as Map<String, dynamic>))
          .toList();
    }
    throw ServerException();
  }

  @override
  Future<bool> removeFriend(int friendId) async {
    try {
      final response =
          await client.delete(FriendshipEndpoints.removeFriend(friendId));
      if ([200, 201].contains(response.statusCode)) {
        return true;
      }
    } catch (err) {
      print(err);
    }
    throw ServerException();
  }

  @override
  Future<Map<String, dynamic>> generateProfileTempCode() async {
    final response =
        await client.get(FriendshipEndpoints.generateProfileTempCode());
    if ([200, 201].contains(response.statusCode)) {
      return response.data;
    } else if (response.statusCode == 400) {
      final data = (response.data) as Map<String, dynamic>;
      final errorMessage =
          data['data'] != null && data['data'].keys.contains('message')
              ? data['data']['message']
              : 'Code invalid or expired';
      throw ServerException(errorMessage);
    }
    throw ServerException();
  }

  @override
  Future<Map<String, dynamic>> validateProfileTempCode(String code) async {
    final response =
        await client.get(FriendshipEndpoints.validateProfileTempCode(code));
    if ([200, 201].contains(response.statusCode)) {
      return response.data;
    } else if (response.statusCode == 400) {
      final data = (response.data) as Map<String, dynamic>;
      final errorMessage =
          data['data'] != null && data['data'].keys.contains('message')
              ? data['data']['message']
              : 'Code invalid or expired';
      throw ServerException(errorMessage);
    }
    throw ServerException();
  }

  @override
  Future<Map<String, dynamic>> generateFriendlyInvitationTempCode() async {
    final response = await client
        .get(FriendshipEndpoints.generateFriendlyInvitationTempCode());
    if ([200, 201].contains(response.statusCode)) {
      return response.data;
    } else if (response.statusCode == 400) {
      final data = (response.data) as Map<String, dynamic>;
      final errorMessage = data.keys.contains('message')
          ? data['message']
          : 'Code invalid or expired';
      throw ServerException(errorMessage);
    }
    throw ServerException();
  }

  @override
  Future<Map<String, dynamic>> validateFriendlyInvitationTempCode(
      String code) async {
    final response = await client
        .get(FriendshipEndpoints.validateFriendlyInvitationTempCode(code));
    if ([200, 201].contains(response.statusCode)) {
      return response.data;
    } else if (response.statusCode == 400) {
      final data = (response.data) as Map<String, dynamic>;
      final errorMessage =
          data['data'] != null && data['data'].keys.contains('message')
              ? data['data']['message']
              : 'Code invalid or expired';
      throw ServerException(errorMessage);
    }
    throw ServerException();
  }

  @override
  Future<bool> blockPlayer(BlockPlayerParamsEntity params) async {
    // Retorno alterado para bool
    final response = await client.post(
      FriendshipEndpoints.blockPlayer(),
      body: params.toJson(),
    );
    if (response.statusCode == 200 || response.statusCode == 204) {
      // Assumindo que a API retorna um corpo que pode indicar sucesso,
      // ou simplesmente o status code é suficiente.
      // Se a API retornar um JSON como {"success": true}, você precisaria parsear:
      // return response.data['success'] == true;
      return true; // Retorna true em caso de sucesso
    }
    // Considerar retornar false ou lançar uma exceção mais específica
    // dependendo de como você quer que o usecase trate falhas de datasource.
    // Lançar ServerException fará com que o repositório retorne Left(ServerFailure()).
    // Se quiser que o usecase receba um 'false' em caso de erro HTTP não fatal,
    // você poderia retornar false aqui e não lançar a exceção para certos status codes.
    print("Error blocking player: ${response.statusCode} - ${response.data}");
    throw ServerException(
        "Failed to block player"); // Mantém o lançamento para falhas inesperadas
  }

  @override
  Future<bool> reportPlayer(ReportPlayerParamsEntity params) async {
    // Retorno alterado para bool
    final response = await client.post(
      FriendshipEndpoints.reportPlayer(), // Endpoint placeholder
      body: params.toJson(), // Usa o método toJson da entidade de parâmetros
    );
    if (response.statusCode == 200 ||
        response.statusCode == 201 ||
        response.statusCode == 204) {
      // Similar ao blockPlayer, ajuste conforme a resposta da sua API.
      // return response.data['success'] == true;
      return true; // Retorna true em caso de sucesso
    }
    print("Error reporting player: ${response.statusCode} - ${response.data}");
    throw ServerException("Failed to report player");
  }

  @override
  Future<List<BlockedPlayerEntity>> getBlockedPlayersList(int userId) async {
    final response =
        await client.get(FriendshipEndpoints.getBlockedPlayersList(userId));
    if (response.statusCode == 200) {
      try {
        final List<dynamic> data = response.data as List<dynamic>;
        return data
            .map((item) =>
                BlockedPlayerEntity.fromJson(item as Map<String, dynamic>))
            .toList();
      } catch (e) {
        throw ServerException(
            'Failed to parse blocked players list: ${e.toString()}');
      }
    }
    throw ServerException(
        'Failed to get blocked players list: ${response.statusCode}');
  }
}
