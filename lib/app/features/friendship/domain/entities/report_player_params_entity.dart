// O enum ReportReason pode ser mantido se você ainda quiser usá-lo internamente
// para tipagem forte antes de converter para string para a API.
// Ou você pode optar por usar diretamente strings para 'reason' na entidade.
// Por ora, manterei o enum e farei a conversão no toJson.

enum ReportReason {
  inappropriateBehavior,
  spam,
  harassment,
  abusiveContent,
  other,
}

class ReportPlayerParamsEntity {
  final int currentUserId; // ID do usuário que está reportando
  final int reportedUserId; // ID do usuário que está sendo reportado
  final ReportReason reasonEnum; // Usando o enum internamente
  final String?
      customReason; // Para o caso de 'other' ou se a API espera uma string livre

  ReportPlayerParamsEntity({
    required this.currentUserId,
    required this.reportedUserId,
    required this.reasonEnum,
    this.customReason, // Necessário se reasonEnum for 'other'
  }) {
    if (reasonEnum == ReportReason.other &&
        (customReason == null || customReason!.isEmpty)) {
      throw ArgumentError(
          'customReason cannot be empty if reasonEnum is ReportReason.other');
    }
  }

  Map<String, dynamic> toJson() {
    String reasonString;
    switch (reasonEnum) {
      case ReportReason.inappropriateBehavior:
        reasonString = "Inappropriate Behavior";
        break;
      case ReportReason.spam:
        reasonString = "Spam";
        break;
      case ReportReason.harassment:
        reasonString = "Harassment";
        break;
      case ReportReason.abusiveContent:
        reasonString = "Abusive Content";
        break;
      case ReportReason.other:
        reasonString = customReason!; // Já validado no construtor
        break;
      default:
        // Caso algum valor do enum não seja coberto,
        // pode-se lançar um erro ou usar um valor padrão.
        // Aqui, usaremos a conversão padrão como fallback,
        // mas o ideal é que todos os casos sejam cobertos.
        reasonString = reasonEnum.toString().split('.').last;
    }

    return {
      'user_id': currentUserId,
      'reported_user_id': reportedUserId,
      'reason': reasonString,
      // 'additionalComments' foi removido pois a API espera apenas 'reason' como string.
      // Se a API aceitasse 'additionalComments' separadamente, ele seria incluído aqui.
    };
  }
}
