import 'package:dartz/dartz.dart';
import 'package:quycky/app/features/friendship/domain/entities/block_player_params_entity.dart';
import 'package:quycky/app/features/friendship/domain/repositories/friendship_repository.dart';
import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/usecase.dart';

class BlockPlayerUsecase implements UseCase<bool, BlockPlayerParamsEntity> {
  // Retorno alterado para bool
  final IFriendshipRepository repository;

  BlockPlayerUsecase(this.repository);

  @override
  Future<Either<Failure, bool>> call(BlockPlayerParamsEntity params) async {
    // Retorno alterado para bool
    // Adicione validações de parâmetros aqui, se necessário, antes de chamar o repositório.
    // Ex: if (params.userIdToBlock == 0) { // Supondo que 0 é inválido
    //   return Left(InvalidInputFailure(message: "User ID to block cannot be 0."));
    // }
    return await repository.blockPlayer(params);
  }
}
