import 'package:dartz/dartz.dart';
import 'package:quycky/app/features/friendship/domain/entities/blocked_player_entity.dart';
import 'package:quycky/app/features/friendship/domain/repositories/friendship_repository.dart';
import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/usecase.dart';

class GetBlockedPlayersListUsecase
    extends UseCase<List<BlockedPlayerEntity>, int> {
  final IFriendshipRepository repository;

  GetBlockedPlayersListUsecase(this.repository);

  @override
  Future<Either<Failure, List<BlockedPlayerEntity>>> call(int userId) async {
    return await repository.getBlockedPlayersList(userId);
  }
}
