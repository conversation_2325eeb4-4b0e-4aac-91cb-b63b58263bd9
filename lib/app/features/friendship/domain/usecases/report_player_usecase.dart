import 'package:dartz/dartz.dart';
import 'package:quycky/app/features/friendship/domain/entities/report_player_params_entity.dart';
import 'package:quycky/app/features/friendship/domain/repositories/friendship_repository.dart';
import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/usecase.dart';

class ReportPlayerUsecase implements UseCase<bool, ReportPlayerParamsEntity> {
  // Retorno alterado para bool
  final IFriendshipRepository repository;

  ReportPlayerUsecase(this.repository);

  @override
  Future<Either<Failure, bool>> call(ReportPlayerParamsEntity params) async {
    // Retorno alterado para bool
    // Adicione validações de parâmetros aqui, se necessário.
    // Ex: if (params.userIdToReport == 0) { // Supondo que 0 é inválido
    //   return Left(InvalidInputFailure(message: "User ID to report cannot be 0."));
    // }
    // if (params.reason == ReportReason.other && (params.additionalComments == null || params.additionalComments!.isEmpty)) {
    //   return Left(InvalidInputFailure(message: "Additional comments are required when reason is 'Other'."));
    // }
    return await repository.reportPlayer(params);
  }
}
