import 'package:flutter/material.dart';
import 'package:intl/intl.dart'; // Para formatação de data/hora
import 'package:quycky/app/features/friendship/domain/entities/chat_message_entity.dart'; // Importando a entidade
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/app_header.dart';
import 'package:quycky/app/widgets/avatar.dart'; // Importando o Avatar
import 'package:quycky/app/widgets/friend_options_bottom_sheet.dart';
import 'package:quycky/app/widgets/gradient_container.dart';
import 'package:quycky/app/widgets/report_player_dialog.dart'; // Re-adicionado
import 'package:quycky/app/widgets/block_player_dialog.dart'; // Re-adicionado
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:sizer/sizer.dart'; // Para ícones
import 'package:uuid/uuid.dart'; // Para gerar IDs únicos

class FriendChat extends StatefulWidget {
  const FriendChat({super.key});

  @override
  State<FriendChat> createState() => _FriendChatState();
}

class _FriendChatState extends State<FriendChat> {
  bool _showInfoBanner = true;
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  double _messageTextFieldBorderRadius = 122;
  final String _currentUserId = 'currentUser'; // Mock ID do usuário atual
  final String _friendUserId = 'friendUser'; // Mock ID do amigo
  final _uuid = Uuid();

  bool get showInfoBanner => _showInfoBanner && _messages.isEmpty;

  set showInfoBanner(bool value) {
    setState(() {
      _showInfoBanner = false;
    });
  }

  // Mock de mensagens usando ChatMessageEntity
  final List<ChatMessageEntity> _messages = [
    ChatMessageEntity(
      id: Uuid().v4(),
      senderId: 'friendUser',
      receiverId: 'currentUser',
      content:
          'Hey! I saw you scored “romantic” on your profile—what does romance mean to you? 👀',
      timestamp: DateTime.now().subtract(Duration(days: 1, minutes: 30)),
      isRead: true,
    ),
    ChatMessageEntity(
      id: Uuid().v4(),
      senderId: 'currentUser',
      receiverId: 'friendUser',
      content:
          'Hi, love that you noticed. Honestly, romance to me isn’t just about grand gestures—it’s about intentionality. Like remembering how someone takes their coffee, sending a song that made me think of them 🤌',
      timestamp: DateTime.now().subtract(Duration(days: 1, minutes: 29)),
      isRead: true,
    ),
    ChatMessageEntity(
      id: Uuid().v4(),
      senderId: 'friendUser',
      receiverId: 'currentUser',
      content:
          'What about you? Are you a fellow romantic or just curious about the vibe?',
      timestamp: DateTime.now().subtract(Duration(days: 1, minutes: 15)),
      isRead: true,
    ),
    ChatMessageEntity(
      id: Uuid().v4(),
      senderId: 'currentUser',
      receiverId: 'friendUser',
      content:
          'I can appreciate the thoughtful gestures, but I also enjoy a good, spontaneous date night.',
      timestamp: DateTime.now().subtract(Duration(days: 1, minutes: 1)),
      isRead: false,
    ),
  ];

  void _sendMessage() {
    if (_messageController.text.isNotEmpty) {
      final newMessage = ChatMessageEntity(
        id: _uuid.v4(),
        senderId: _currentUserId,
        receiverId: _friendUserId,
        content: _messageController.text,
        timestamp: DateTime.now(),
        isRead: false,
      );
      setState(() {
        _messages.add(newMessage);
        _messageController.clear();
      });
      // Rolar para a última mensagem
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    }
    _handleChangeMessageTextFieldBorderRadius();
  }

  void handleBack() {}

  void openMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext bottomSheetContext) {
        return FriendOptionsBottomSheet(
          doInviteToPlay: _doInviteToPlay,
          doEraseChatHistory: _doEraseChatHistory,
          doUnmatchPlayer: _doUnmatchPlayer,
          doReportPlayer: () =>
              _doReportPlayer(context), // Passa o context da tela
          doBlockPlayer: () =>
              _doBlockPlayer(context), // Passa o context da tela
        );
      },
    );
  }

  // Funções de ação para o BottomSheet
  void _doInviteToPlay() {
    // TODO: Implementar lógica para convidar para jogar
    print("Ação: Convidar para jogar");
  }

  void _doEraseChatHistory() {
    // TODO: Implementar lógica para apagar histórico do chat
    print("Ação: Apagar histórico do chat");
  }

  void _doUnmatchPlayer() {
    // TODO: Implementar lógica para descombinar jogador
    print("Ação: Descombinar jogador");
  }

  void _doReportPlayer(BuildContext pageContext) {
    showDialog(
      context: pageContext, // Usa o context da página para o showDialog
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return ReportPlayerDialog(
          onBlockPlayer: () {
            Navigator.of(dialogContext).pop(); // Fecha o ReportPlayerDialog
            _doBlockPlayer(
                pageContext); // Chama a função de bloquear com o context da página
            // Lógica adicional para bloquear jogador pode ser adicionada aqui se necessário
            print(
                "Ação: Bloquear jogador após reporte (chamado de FriendChat)");
          },
          onReturn: () {
            // A lógica de fechar o diálogo já está em ReportPlayerDialog
            print("Ação: Retornar do reporte (chamado de FriendChat)");
          },
        );
      },
    );
  }

  void _doBlockPlayer(BuildContext pageContext) {
    // Recebe o context da página
    showDialog(
      context: pageContext, // Usa o context da página para o showDialog
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return BlockPlayerDialog(
          onDone: () {
            // A lógica de fechar o diálogo já está em BlockPlayerDialog
            // Lógica adicional após confirmação de bloqueio pode ser adicionada aqui
            print(
                "Ação: Jogador bloqueado, diálogo de confirmação fechado (chamado de FriendChat)");
          },
        );
      },
    );
  }

  PreferredSize _buildAppHeader() {
    final friendName = "WAGNER"; // Mock
    final friendStatus = "last online 2m ago"; // Mock
    final friendAvatarUrl = "assets/img/png/profile_test.png"; // Mock
    final height = showInfoBanner ? 9.4.h : 11.h;
    return PreferredSize(
      preferredSize: Size.fromHeight(height),
      child: SafeArea(
        child: AppHeader(
          logoSectionLeftWidget: IconButton(
              onPressed: handleBack,
              icon: const Icon(
                QuyckyIcons.arrow_left_circle,
                color: Colors.white,
                size: 23,
              )),
          logoSectionRightWidget: IconButton(
              onPressed: openMenu,
              icon: const Icon(Icons.more_vert, color: Colors.white)),
          centerChild: Container(
            color: Colors.transparent,
            width: 67.w,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Avatar(
                  imagePath: friendAvatarUrl,
                  size: 6.87.h,
                  addPhotoButton: false,
                  borderColor: Colors.white,
                  borderWidth: 1.5,
                ),
                SizedBox(width: 4.1.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      friendName,
                      style: const TextStyle(
                          fontFamily: 'Roboto',
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          letterSpacing: 0.1),
                    ),
                    Text(
                      friendStatus,
                      style: const TextStyle(
                        fontFamily: 'Roboto',
                        color: Colors.white,
                        letterSpacing: -0.2,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // A cor de fundo da imagem é um rosa/laranja. Usarei CustomColors.primary
    // que é 0xfff85a3e, um laranja avermelhado.
    // Para os balões do "outro", a cor é a mesma do fundo.
    // Para os balões "meus", a cor é um rosa mais claro. Vou usar CustomColors.charmPink (0xffE685A2) por enquanto.
    // Se não existir, usarei um tom aproximado ou CustomColors.lightCrimson (0xfff36789)

    final chatListItems = _buildChatListItems();

    return GradientContainer(
      useDefault: true,
      coldOpacity: 0,
      normalOpacity: 0,
      hotOpacity: 0,
      child: Scaffold(
        backgroundColor: Colors.transparent, // Cor de fundo da tela
        appBar: _buildAppHeader(),
        body: Column(
          children: [
            if (showInfoBanner) _buildInfoBanner(), // Adiciona o banner aqui
            Expanded(
              child: ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.all(16.0),
                itemCount: chatListItems.length,
                itemBuilder: (context, index) {
                  return chatListItems[index];
                },
              ),
            ),
            _buildMessageInputField(),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildChatListItems() {
    final List<Widget> items = [];
    bool todaySeparatorShown = false;
    final now = DateTime.now();

    for (int i = 0; i < _messages.length; i++) {
      final message = _messages[i];
      final messageDate = message.timestamp;

      // Verifica se a mensagem é de hoje
      final bool isMessageToday = messageDate.year == now.year &&
          messageDate.month == now.month &&
          messageDate.day == now.day;

      if (isMessageToday && !todaySeparatorShown) {
        items.add(_buildDateSeparator("Today"));
        todaySeparatorShown = true;
      }
      // TODO: Adicionar lógica para outros separadores de data (ex: "Yesterday", "dd/MM/yyyy")
      // else if (!isMessageToday) {
      //   // Lógica para mostrar outras datas se necessário, comparando com a data da mensagem anterior
      //   if (i == 0 ||
      //       _messages[i-1].timestamp.year != messageDate.year ||
      //       _messages[i-1].timestamp.month != messageDate.month ||
      //       _messages[i-1].timestamp.day != messageDate.day) {
      //         // items.add(_buildDateSeparator(DateFormat('dd MMM').format(messageDate)));
      //       }
      // }
      items.add(_buildMessageBubble(message));
    }
    return items;
  }

  Widget _buildInfoBanner() {
    return Container(
      color: CustomColors.primary, // Cor de fundo do banner
      padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 0.5.h),
      child: Row(
        children: [
          IconButton(
            icon: Icon(QuyckyIcons.close_circle,
                color: Colors.white, size: 2.97.h),
            onPressed: () {
              showInfoBanner = false;
            },
          ),
          const SizedBox(width: 8.0),
          Expanded(
              child: RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                letterSpacing: 0.2,
                height: 1.2,
                fontFamily: 'Roboto',
              ),
              children: <TextSpan>[
                TextSpan(text: 'Messages will vanish '),
                TextSpan(
                  text: '24 hours\n',
                  style: TextStyle(
                      fontFamily: 'Roboto', fontWeight: FontWeight.bold),
                ),
                TextSpan(text: 'after they are sent. - '),
                TextSpan(
                  text: 'Don’t miss out!',
                  style: TextStyle(
                      fontFamily: 'Roboto', fontWeight: FontWeight.bold),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  void _handleChangeMessageTextFieldBorderRadius() {
    setState(() {
      _messageTextFieldBorderRadius =
          _messageController.text.isEmpty ? 122 : 20;
    });
  }

  Widget _buildDateSeparator(String date) {
    return Center(
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 10.0),
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
        decoration: BoxDecoration(
          color: CustomColors.sunsetOrange,
          borderRadius: BorderRadius.circular(122.0),
        ),
        child: Text(
          date,
          style: const TextStyle(color: Colors.white, fontSize: 12.0),
        ),
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessageEntity message) {
    final bool isMe = message.senderId == _currentUserId;
    final align = isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start;
    // A imagem mostra que o balão do "outro" é da mesma cor do fundo, mas o texto é branco.
    // O balão "meu" é um rosa claro.
    // Usando as cores definidas anteriormente, mas adaptando para a lógica de isMe
    final actualBubbleColor = isMe
        ? CustomColors
            .primary // Cor para "minhas" mensagens (rosa/laranja claro)
        : CustomColors
            .sunsetOrange; // Cor para mensagens "do outro" (laranja mais escuro)

    // Formatar o timestamp
    final formattedTime =
        DateFormat('HH:mm').format(message.timestamp); // Ex: 16:30

    return Column(
      crossAxisAlignment: align,
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 4.0),
          padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
          decoration: BoxDecoration(
            color: actualBubbleColor,
            borderRadius: BorderRadius.only(
              topLeft: const Radius.circular(20.0),
              topRight: const Radius.circular(20.0),
              bottomLeft: isMe
                  ? const Radius.circular(20.0)
                  : const Radius.circular(0.0),
              bottomRight: isMe
                  ? const Radius.circular(0.0)
                  : const Radius.circular(20.0),
            ),
          ),
          child: Text(
            message.content,
            style: const TextStyle(color: Colors.white, fontSize: 15),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 2.0, bottom: 8.0),
          child: Text(
            formattedTime, // Usar o tempo formatado
            style:
                TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 10.0),
          ),
        )
      ],
    );
  }

  Widget _buildMessageInputField() {
    return Container(
      color: Colors.transparent,
      padding: EdgeInsets.only(left: 6.41.w, right: 6.41.w, bottom: 3.h),
      // Cor de fundo da área de input
      child: SafeArea(
        // Garante que não fique sob elementos do sistema (como a barra de gestos do iOS)
        child: Row(
          children: [
            Expanded(
              child: AnimatedContainer(
                duration: Duration(seconds: 1),
                width: 86.92.w,
                decoration: BoxDecoration(
                  color:
                      CustomColors.orangeSoda, // Cor de fundo do campo de texto
                  borderRadius: BorderRadius.circular(
                      _messageTextFieldBorderRadius), // Bordas arredondadas consistentes
                ),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: 21.32.h,
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 2.h),
                    child: TextField(
                      controller: _messageController,
                      style: const TextStyle(color: Colors.white),
                      minLines: 1,
                      maxLines: null,
                      textInputAction: TextInputAction.send,
                      keyboardType: TextInputType.multiline,
                      decoration: InputDecoration(
                        hintText: 'Type something...',
                        hintStyle: TextStyle(
                            color: Colors.white,
                            fontFamily: 'Roboto',
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                            letterSpacing: 0.2),
                        border: InputBorder
                            .none, // Remove a borda padrão do TextField
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 20.0,
                            vertical: 10.0), // Padding interno do texto
                        suffixIcon: Padding(
                          padding: EdgeInsets.only(right: 2.w),
                          child: IconButton(
                            icon: Icon(
                              QuyckyIcons.send,
                              color: Colors.white,
                              size: 3.2.h,
                            ),
                            onPressed: _sendMessage,
                          ),
                        ),
                      ),
                      onChanged: (value) =>
                          _handleChangeMessageTextFieldBorderRadius(),
                      onSubmitted: (_) => _sendMessage(),
                    ),
                  ),
                ),
              ),
            ),
            // O SizedBox e o Material do botão de enviar foram removidos
          ],
        ),
      ),
    );
  }
}
