import 'package:flutter_triple/flutter_triple.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_invite_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_list_item_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_profile_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_store_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/game_invite_entity.dart';

class FriendshipStore extends Store<FriendshipStoreEntity> {
  FriendshipStore()
      : super(const FriendshipStoreEntity(
            friendshipListItems: [],
            friendshipInviteReceivedListItems: [],
            friendshipInviteSentListItems: [],
            invites: [],
            invitesReceived: [],
            friendships: [],
            gameInvites: []));

  defineLoadingState(bool loadingState) {
    setLoading(loadingState);
  }

  setData(FriendshipStoreEntity data) {
    update(data);
  }

  clear() => setData(const FriendshipStoreEntity(
      friendshipListItems: [],
      friendshipInviteReceivedListItems: [],
      friendshipInviteSentListItems: [],
      invites: [],
      invitesReceived: [],
      friendships: [],
      gameInvites: []));
  setInvites(List<FriendshipInviteEntity> invites) {
    update(state.copyWith(invites: invites), force: true);
  }

  setGameInvites(List<GameInviteEntity> gameInvites) {
    update(state.copyWith(gameInvites: gameInvites), force: true);
  }

  setInvitesReceived(List<FriendshipInviteEntity> invites) {
    update(state.copyWith(invitesReceived: invites), force: true);
  }

  setFriendshipListItems(List<FriendshipListItem> friendshipListItems) {
    update(state.copyWith(friendshipListItems: friendshipListItems),
        force: true);
  }

  setFriendProfile(FriendProfileEntity? friendProfile) {
    update(state.copyWith(friendProfile: friendProfile), force: true);
  }

  setFriendships(List<FriendshipEntity> friendships) {
    update(state.copyWith(friendships: friendships), force: true);
  }
}
