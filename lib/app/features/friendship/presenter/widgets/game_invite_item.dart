import 'dart:async';
import 'package:flutter/material.dart';
import 'package:quycky/app/features/friendship/domain/entities/game_invite_entity.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/avatar.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/core/utils/image_assets.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:sizer/sizer.dart';

class GameInviteItem extends StatefulWidget {
  final GameInviteEntity invite;
  final void Function(String id) onAccept;
  final void Function(String id) onCancel;
  const GameInviteItem(
      {super.key,
      required this.invite,
      required this.onAccept,
      required this.onCancel});

  @override
  State<GameInviteItem> createState() => _GameInviteItem();
}

class _GameInviteItem extends State<GameInviteItem> {
  Timer? _timer;
  Duration _timeRemaining = Duration.zero;

  @override
  void initState() {
    super.initState();
    _updateTimeRemaining();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _updateTimeRemaining() {
    final now = DateTime.now();
    final difference = widget.invite.expiresAt.difference(now);

    if (difference.isNegative || difference.inSeconds <= 0) {
      setState(() {
        _timeRemaining = Duration.zero;
      });
      // Automatically call onCancel when timer reaches zero
      widget.onCancel(widget.invite.id);
    } else {
      setState(() {
        _timeRemaining = difference;
      });
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateTimeRemaining();
    });
  }

  String _formatDuration(Duration duration) {
    if (duration.inSeconds <= 0) {
      return '00:00';
    }

    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  Widget getButton() => SizedBox(
        width: 23.33.w,
        height: 4.38.h,
        child: Button(
          onPressed: () => widget.onAccept(widget.invite.id),
          autoSized: true,
          child: Text(
            'ACCEPT',
            style: buttonDefaultTextStyle(Colors.white,
                fontSize: 12, letterSpacing: 1.8),
          ),
        ),
      );
  Widget getIconButton() {
    return IconButton(
        onPressed: () => widget.onCancel(widget.invite.id),
        icon: const Icon(
          QuyckyIcons.close_circle,
          color: Colors.white,
          size: 25,
        ));
  }

  Widget getWidget() {
    return Container(
      height: 10.66.h,
      constraints: BoxConstraints(maxWidth: 87.18.w),
      decoration: const BoxDecoration(
        color: CustomColors.orangeSoda30,
        borderRadius: BorderRadius.all(Radius.circular(25)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: 58.12.w,
            child: Row(
              children: [
                SizedBox(
                  width: 1.w,
                ),
                getIconButton(),
                Padding(
                  padding: EdgeInsets.only(left: 2.8.w, right: 5.38.w),
                  child: Avatar(
                      imagePath: widget.invite.player.avatarUrl,
                      size: 6.87.h,
                      addPhotoButton: false),
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 20.w,
                      child: Text(
                        widget.invite.player.name,
                        style: const TextStyle(
                            overflow: TextOverflow.ellipsis,
                            fontFamily: "Roboto",
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 14),
                      ),
                    ),
                    SizedBox(
                      height: 1.5.h,
                    ),
                    Text(
                      _formatDuration(_timeRemaining),
                      style: TextStyle(
                          fontFamily: "Roboto",
                          fontWeight: FontWeight.bold,
                          color: _timeRemaining.inSeconds <= 30
                              ? Colors.red
                              : Colors.white,
                          fontSize: 14),
                    ),
                  ],
                ),
              ],
            ),
          ),
          getButton(),
          SizedBox(width: 4.w),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return getWidget();
  }
}
