import 'dart:io';

import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:quycky/app/features/friendship/presenter/controllers/friendship_controller.dart';
import 'package:quycky/app/features/game/domain/entities/friend_invite_params_entity.dart';
import 'package:quycky/app/features/game/domain/entities/game_entity.dart';
import 'package:quycky/app/features/game/domain/entities/game_friend_invite_entity.dart';
import 'package:quycky/app/features/game/presenter/controllers/game_controller.dart';
import 'package:quycky/app/features/game/presenter/pages/widgets/friend_invite/friends_invite_bottom_sheet.dart';
import 'package:quycky/app/features/game/presenter/pages/widgets/lobby_countdown.dart';
import 'package:quycky/app/features/game/presenter/pages/widgets/players_orbit.dart';
import 'package:quycky/app/features/game/presenter/store/game_store.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/app/widgets/app_header.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/gradient_container.dart';
import 'package:quycky/core/utils/interval_utils.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:sizer/sizer.dart';

class LobbyPage extends StatefulWidget {
  final UserEntity? friend;
  final bool keepPlaying;
  final String title;

  const LobbyPage(
      {super.key,
      this.title = 'LobbyPage',
      this.friend,
      this.keepPlaying = false});

  @override
  LobbyPageState createState() => LobbyPageState();
}

class LobbyPageState extends State<LobbyPage>
    with SingleTickerProviderStateMixin {
  // final _gameStorage = Modular.get<GameStorage>();
  late final GameController _controller;
  late final FriendshipController _friendshipController;
  late final GameStore _gameStore;
  late final AnimationController _circlesAnimationController;
  bool _isGameModeChosen = false;

  set isGameModeChosen(bool value) {
    setState(() {
      _isGameModeChosen = value;
    });
  }

  @override
  void initState() {
    super.initState();
    _circlesAnimationController = AnimationController(vsync: this);
    _circlesAnimationController.repeat(
        min: 0, max: 1, period: const Duration(seconds: 8));

    _controller = Modular.get<GameController>();
    _friendshipController = Modular.get<FriendshipController>();
    _gameStore = Modular.get<GameStore>();
    _gameStore.triple.clearError();
    // QuestionTemperatureSliderStore questionTemperatureSliderStore =
    //     Modular.get<QuestionTemperatureSliderStore>();

    // _backgroundOpacities = getBackgroundOpacitiesByQuestionTemperature(
    //     questionTemperatureSliderStore.state);

    // _controller.startRestartController(widget.friend);
    final lastGameMode = _gameStore.state.gameMode;
    _controller.startRestartController(widget.friend);
    _verifyIfGameModeIsChosenAndStart(lastGameMode);
  }

  void _verifyIfGameModeIsChosenAndStart(EGameMode lastGameMode) {
    if (widget.friend != null) {
      isGameModeChosen = true;
      return;
    }
    if (widget.keepPlaying) {
      _gameStore.setGameMode(lastGameMode);
      _handleStartGame(lastGameMode == EGameMode.oneOnInvitedPlayers);
      isGameModeChosen = true;
    }
  }

  void enterGame({addBots = true}) {
    _controller.enterGame(addBots: addBots);
  }

  void handleGoToGamePlayPage() {
    Modular.to.pushReplacementNamed(AppRoutes.gamePlay());
  }

  Widget getTextWidgetSubtitleCaptionOld(String subtitle, String caption) =>
      Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 20.0, bottom: 15),
            child: Text(
              subtitle,
              style: const TextStyle(
                  fontFamily: "Roboto",
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 15),
            ),
          ),
          Text(
            caption,
            style: const TextStyle(
                letterSpacing: 1,
                fontFamily: "Roboto",
                fontWeight: FontWeight.w600,
                color: Colors.white,
                fontSize: 12),
          ),
        ],
      );

  handleOpenQrCodePage() {
    Modular.to.pushNamed(AppRoutes.readGameQrCode());
  }

  handleBack() {
    _controller.stopTimerToStartSocket();
    _controller.stopSocket();
    Modular.to.pushNamedAndRemoveUntil(
        AppRoutes.home, ModalRoute.withName(AppRoutes.home));
  }

  Widget getTextWidgetSubtitleCaption(String title, String caption) {
    UserEntity waitingForThisFriend =
        _controller.friendImWaitingToAcceptTheInvitation.userData;
    return AppHeader(
        title: _gameStore.state.gameMode == EGameMode.oneOnInvitedPlayers
            ? "LET'S PLAY"
            : title,
        subtitle: _gameStore.state.gameMode == EGameMode.oneOnInvitedPlayers
            ? _controller.socket != null && _controller.socket!.active
                ? ((waitingForThisFriend.name == '' &&
                        _gameStore.state.invitedPlayers.isNotEmpty)
                    ? (_gameStore.state.invitedPlayers.length < 3
                        ? 'START MATCH OR INVITE MORE FRIENDS'
                        : (_gameStore.state.gameStartStage ==
                                EGameStartStage.preparing
                            ? 'THE GAME WILL START SHORTLY'
                            : ''))
                    : 'WAITING FOR ${waitingForThisFriend.name != '' ? waitingForThisFriend.name : 'FRIEND TO JOIN'}') // : ('Waiting for friend to join')
                : 'INVITE A FRIEND TO PLAY!'
            : caption,
        logoSectionLeftWidget: IconButton(
            onPressed: handleBack,
            icon: const Icon(
              QuyckyIcons.arrow_left_circle,
              color: Colors.white,
              size: 23,
            )),
        logoSectionRightWidget: IconButton(
            onPressed: handleOpenQrCodePage,
            icon: const Icon(QuyckyIcons.read_qr_code, color: Colors.white)));
  }

  Widget getButton() {
    return Button(
      textColor: CustomColors.orangeSoda,
      outlined: true,
      onPressed: () => print(''),
      text: "Accept",
    );
  }

  getErrorWidget() {
    return [
      const Center(
        child: Text(
          "ALL THE ROOMS ARE FULL.\nPLEASE TRY AGAIN!",
          // "CURRENTLY,\nTHERE ARE NO AVAILABLE\nQUYCKY PLAYERS.",
          textAlign: TextAlign.center,
          style: TextStyle(
              letterSpacing: 4,
              fontFamily: "Roboto",
              fontWeight: FontWeight.bold,
              color: Colors.white,
              fontSize: 14),
        ),
      ),
      const Spacer(),
      Container(
          margin: const EdgeInsets.only(bottom: 45),
          constraints: const BoxConstraints(maxWidth: 350),
          height: 50,
          width: double.infinity,
          child: Button(
              autoSized: true,
              onPressed: () => Modular.to
                  .pushNamedAndRemoveUntil(AppRoutes.home, (p0) => false),
              text: 'Continue')),
    ];
  }

  void onInviteFriendsBottomSheetClose(UserEntity? friendData,
      {int pos = 0, bool? shareLink}) {
    _controller.isModalBottomSheetOpened = false;

    if (friendData != null || shareLink == true) {
      _controller.setDataOfFriendToInvite(GameFriendInviteEntity(
          invitePos: pos,
          friendInvite: FriendInviteParamsEntity(
              friendIdentifier: friendData?.identifier ?? 'share',
              roomName: ''),
          friendData: friendData));
      if (_controller.alreadyInARoom) {
        _controller.inviteFriendFromStoreData();
        return;
      }
      enterGame(addBots: false);
      return;
    }
    // _gameStore.setGameMode(EGameMode.fourRandom);
    // _controller.startTimerToStartSocket();
  }

  void handleOnInviteFriendsPressed({int pos = 0}) async {
    if (_controller.isModalBottomSheetOpened) return;
    _gameStore.setGameMode(EGameMode.oneOnInvitedPlayers);

    // if (_controller.friends.isEmpty) {
    await _friendshipController.getAllFriendshipData();
    // }
    _controller.isModalBottomSheetOpened = true;

    showModalBottomSheet<void>(
        context: context,
        isDismissible: false,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) => FriendsInviteBottomSheet(
              currentUser: _controller.localPlayer,
              friends: _controller.friends,
              playerPosition: pos,
              onClose: onInviteFriendsBottomSheetClose,
            )).whenComplete(() {
      _controller.isModalBottomSheetOpened = false;
    });
  }

  Widget getStartGameWidget() {
    if (_gameStore.state.gameMode == EGameMode.oneOnInvitedPlayers &&
        _gameStore.state.showAllInviteSlots &&
        _gameStore.state.gameStartStage == EGameStartStage.none) {
      Function()? func;
      _controller.startGame;
      var opacity = 0.75;
      if (_controller.asAnyInvitedFriendAlreadyAccepted) {
        func = _controller.startGame;
        opacity = 1;
      }

      return Opacity(
        opacity: opacity,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 0),
          constraints: const BoxConstraints(maxWidth: 380),
          width: 380,
          child: Button(
            text: 'PLAY',
            onPressed: func,
          ),
        ),
      );
    }
    return Container();
  }

  void _handleStartGame([withFriends = false]) {
    _controller.startRestartController(widget.friend);
    isGameModeChosen = true;
    if (withFriends) {
      _gameStore.setGameMode(EGameMode.oneOnInvitedPlayers);
      setTimeout(
          callback: () {
            handleOnInviteFriendsPressed(pos: 1);
          },
          duration: Duration(milliseconds: 500));
    } else {
      _gameStore.setGameMode(EGameMode.fourRandom);
      setTimeout(
          callback: () {
            _controller.enterGame(addBots: true);
          },
          duration: Duration(milliseconds: 3000));
    }
  }

  Widget getBottomChooseGameModeButtons() {
    if (widget.friend != null || !_controller.isRoomOwner) return Container();
    return Column(
      children: [
        SizedBox(
            height: 6.05.h,
            width: 90.w,
            child: Button(
                onPressed: _handleStartGame, text: 'Play', autoSized: true)),
        SizedBox(
          height: 1.77.h,
        ),
        SizedBox(
            height: 6.05.h,
            width: 90.w,
            child: Button(
                onPressed: () => _handleStartGame(true),
                borderColor: Colors.white,
                outlined: true,
                text: 'Play With FRIENDS',
                autoSized: true)),
      ],
    );
  }

  Widget getBottomButtons() {
    return SizedBox(
      height: 20.4.h,
      child: _isGameModeChosen
          ? (_controller.isRoomOwner &&
                  _gameStore.state.currentState == EGameState.waitingPlayers)
              ? Align(
                  alignment: Alignment.center,
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(
                          top: 0,
                        ),
                        child: getStartGameWidget(),
                      )
                    ],
                  ),
                )
              : Container()
          : getBottomChooseGameModeButtons(),
    );
  }

  getLobbyWidget(EGameState currentState) {
    return [
      Container(
        width: double.infinity,
        height: MediaQuery.of(context).size.height * 0.60,
        padding: EdgeInsets.only(bottom: Platform.isIOS ? 50 : 28),
        constraints: const BoxConstraints(maxHeight: 380),
        child: Stack(
          alignment: Alignment.center,
          children: [
            SizedBox(
              width: 400,
              height: 400,
              child: Lottie.asset("assets/animations/circles.json",
                  controller: _circlesAnimationController,
                  fit: BoxFit.contain,
                  width: 400,
                  height: 400),
            ),
            Center(
              child: Container(
                  constraints:
                      const BoxConstraints(maxHeight: 400, maxWidth: 400),
                  child: currentState == EGameState.starting
                      ? LobbyCountdown(goToNextPage: handleGoToGamePlayPage)
                      : PlayersOrbit(
                          fromVibeCheckPage: widget.friend != null,
                          localPlayer: _controller.localPlayer,
                          players: _controller.playersWithoutLocalPlayer,
                          showAllInviteSlots:
                              _gameStore.state.showAllInviteSlots,
                          showButtonAddFriends: _isGameModeChosen &&
                              _gameStore.state.gameMode ==
                                  EGameMode.oneOnInvitedPlayers &&
                              (!_gameStore.state.room.roomName!
                                      .contains('room--') &&
                                  _gameStore.state.isRoomOwner),
                          gameMode: _gameStore.state.gameMode,
                          hasLoggedUserEverHadAnyMatches: true,
                          //_controller.hasLoggedUserEverHadAnyMatches,
                          invitedPlayers: _gameStore.state.invitedPlayers,
                          setShowAllInviteSlots:
                              _gameStore.setShowAllInviteSlots,
                          onInviteFriendsPressed:
                              handleOnInviteFriendsPressed)),
            ),
          ],
        ),
      ),
      getBottomButtons(),
      // const Spacer(),
      // const Text(
      //   'With respect, curiosity and love!',
      //   style: TextStyle(
      //       letterSpacing: 1,
      //       fontFamily: "Roboto",
      //       fontWeight: FontWeight.w600,
      //       color: Colors.white,
      //       fontSize: 13),
      // ),
      // SizedBox(
      //   height: 18,
      // )
    ];
  }

  getBody(Triple<GameEntity> tripleData) {
    int totalUsers = 4 - (tripleData.state.room.users.length);
    bool hasError = tripleData.error != null;
    String title = 'MATCHING!';
    String subtitle = '';
    if (hasError) {
      title = '';
      subtitle = '';
    } else {
      subtitle = !_isGameModeChosen
          ? 'CHOOSE YOUR MODE'
          : (totalUsers <= 0
              ? 'GAME IS STARTING!'
              : 'WAITING FOR ${totalUsers > 3 ? 3 : totalUsers} MORE PLAYER(S)');
    }
    return Padding(
      padding: const EdgeInsets.only(top: 0.0),
      child: Column(
        children: [
          getTextWidgetSubtitleCaption(title, subtitle),
          Spacer(),
          ...(hasError
              ? getErrorWidget()
              : getLobbyWidget(tripleData.state.currentState)),
        ],
      ),
    );
  }
  // GradientContainer(
  //           coldOpacity: _backgroundOpacities.coldOpacity,
  //           normalOpacity: _backgroundOpacities.normalOpacity,
  //           hotOpacity: _backgroundOpacities.hotOpacity,

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
          body: GradientContainer(
              useDefault: true,
              coldOpacity: 0,
              normalOpacity: 0,
              hotOpacity: 0,
              child: SafeArea(
                  child: Center(
                child: TripleBuilder(
                    store: _gameStore,
                    builder: (context, tripleObj) {
                      Triple<GameEntity> triple =
                          tripleObj as Triple<GameEntity>;
                      return getBody(triple);
                    }),
              )))),
    );
  }
//
// JavascriptChannel _toasterJavascriptChannel(BuildContext context) {
//   return JavascriptChannel(
//       name: 'Toaster',
//       onMessageReceived: (JavascriptMessage message) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(content: Text(message.message)),
//         );
//       });
// }
}
