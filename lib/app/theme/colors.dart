import 'package:flutter/material.dart';

Color hexToColor(String colorHex, {String alphaChannel = 'FF'}) {
  return Color(int.parse(colorHex.replaceFirst('#', '0x$alphaChannel')));
}

Color rgbToColor(String rgb) {
  Color res = CustomColors.primary;
  try {
    List<String> parts = rgb
        .replaceFirst('rgb', '')
        .replaceFirst('(', '')
        .replaceFirst(')', '')
        .split(',');
    int red = int.parse(parts[0]);
    int green = int.parse(parts[1]);
    int blue = int.parse(parts[2]);
    res = Color.fromRGBO(red, green, blue, 1);
  } catch (err) {
    debugPrint('rtC=>$err');
  }
  return res;
}

Color stringToColor(String color) {
  if (color.contains('#')) {
    return hexToColor(color);
  }
  return rgbToColor(color);
}

MaterialColor createMaterialColor(Color color) {
  List strengths = <double>[.05];
  Map<int, Color> swatch = {};
  final int r = color.red, g = color.green, b = color.blue;

  for (int i = 1; i < 10; i++) {
    strengths.add(0.1 * i);
  }
  for (var strength in strengths) {
    final double ds = 0.5 - strength;
    swatch[(strength * 1000).round()] = Color.fromRGBO(
      r + ((ds < 0 ? r : (255 - r)) * ds).round(),
      g + ((ds < 0 ? g : (255 - g)) * ds).round(),
      b + ((ds < 0 ? b : (255 - b)) * ds).round(),
      1,
    );
  }
  return MaterialColor(color.value, swatch);
}

enum EColors {
  primary,
  secondary,
  accent,
  background,
  surface,
  error,
  text,
  onSurface,
  disabled,
  placeholder,
  backdrop,
  notification,
  lightCrimson,
  wildWatermelon,
  americanPurple,
  charmPink,
  orangeSoda,
  button,
}

final Map<EColors, Color> colors = {
  EColors.primary: const Color(0xfff85a3e),
  EColors.secondary: const Color(0xff26a69a),
  EColors.accent: const Color(0xff9c27b0),
  EColors.background: const Color(0xfff6f6f6),
  EColors.surface: const Color(0xffffffff),
  EColors.error: const Color(0xffB00020),
  EColors.text: const Color(0xff000000),
  EColors.onSurface: const Color(0xff000000),
  EColors.disabled: const Color(0x00000042),
  EColors.placeholder: const Color(0x0000008A),
  EColors.backdrop: const Color(0x00000080),
  EColors.notification: const Color(0xffA9FAAE),
  EColors.wildWatermelon: const Color(0xffff4e7a),
  EColors.lightCrimson: const Color(0xfff36789),
  EColors.americanPurple: const Color(0xff471249),
  EColors.charmPink: const Color(0xffE685A2),
  EColors.orangeSoda: const Color(0xfff85a3e),
  EColors.button: const Color(0xfff85a3e)
};

Color getAppColor(EColors eColors) {
  return colors[eColors] ?? Colors.white;
}

MaterialColor getAppMaterialColor(EColors eColors) {
  return createMaterialColor(colors[eColors] ?? Colors.white);
}

///  * Cookbook: [Use themes to share colors and font styles](https://flutter.dev/docs/cookbook/design/themes)
class CustomColors {
  CustomColors._();
  static const Color primary = Color(0xfff85a3e);
  static const Color secondary = Color(0xff26a69a);
  static const Color accent = Color(0xff9c27b0);
  static const Color background = Color(0xfff6f6f6);
  static const Color surface = Color(0xffffffff);
  static const Color error = Color(0xffB00020);
  static const Color text = Color(0xff000000);
  static const Color onSurface = Color(0xff000000);
  static const Color disabled = Color(0x00000042);
  static const Color placeholder = Color(0x0000008A);
  static const Color backdrop = Color(0x00000080);
  static const Color notification = Color(0xffA9FAAE);
  static const Color lightCrimson = Color(0xfff36789);
  static const Color wildWatermelon = Color(0xffff4e7a);
  static const Color americanPurple = Color(0xff471249);
  static const Color americanPurple2 = Color(0xff46124A);
  static const Color americanPurple3_70 = Color(0xB3fF9696);

  static const Color irresistible = Color(0xffB3396F);
  static const Color charmPink = Color(0xffE685A2);

  static const Color orangeSoda = Color(0xFFf85a3e);

  static const Color orangeSoda90 = Color(0xE6f85a3e);

  static const Color orangeSoda70 = Color(0xB3f85a3e);

  static const Color orangeSoda60 = Color(0x99f85a3e);

  static const Color orangeSoda54 = Color(0x8Af85a3e);

  static const Color orangeSoda40 = Color(0x66f85a3e);

  static const Color orangeSoda45 = Color(0x73f85a3e);

  static const Color orangeSoda38 = Color(0x62f85a3e);

  static const Color orangeSoda30 = Color(0x4Df85a3e);

  static const Color orangeSoda24 = Color(0x3Df85a3e);

  static const Color orangeSoda12 = Color(0x1Ff85a3e);

  static const Color orangeSoda10 = Color(0x1Af85a3e);

  static const Color button = Color(0xfff85a3e);

  static const Color cinnamonSatin = Color(0xffd0597d);

  static const Color chineseWhite = Color(0xffe1e6e1);

  static const Color paleCerulean = Color(0xff9ccbda);

  static const Color paleCerulean50 = Color(0x809ccbda);

  static const Color darkPurple = Color(0xff310637);
  static const Color melon = Color(0xffFFB9AD);
  static const Color melon_2 = Color(0xffFFc2b2);

  static const Color lavenderGray = Color(0xffccc1d1);
  static const Color lavenderGray70 = Color(0xB3ccc1d1);
  static const Color oriolesOrange = Color(0xfffc4524);
  static const Color veryLightTangelo = Color(0xffffae73);
  static const Color brinkPink = Color(0xFFFF5F7C);
  static const Color salmonPink = Color(0xffFFAEA1);
  static const Color salmon = Color(0xffff8570);
  static const Color salmon_2 = Color(0xfff47E75);
  static const Color salmon_3 = Color(0xfff7836e);
  static const Color salmon_4 = Color(0xffF7846F);
  static const Color gainsbora = Color(0xffdfdfdf);
  static const Color maximumPurple = Color(0xff733D77);
  static const Color seaGreen = Color(0xff34a853);
  static const Color seaGreen90 = Color(0xE634a853);
  static const Color uclaGold = Color(0xfffbb505);
  static const Color peachOrange = Color(0xffffC199);
  static const Color cultured = Color(0xffF8F8F8);
  static const Color cultured90 = Color(0xe6F8F8F8);
  static const Color bitterSweet = Color(0xffFA6E5E);
  static const Color fieryRose = Color(0xffF5576C);
  static const Color davysGrey = Color(0xff595859);
  static const Color mauvelous = Color(0xffF6A8A8);
  static const Color lightSilver = Color(0xffD9D9D9);
  static const Color maximumYellowRed = Color(0xfffbbf44);
  static const Color mediumSeaGreen = Color(0xff34ab74);
  static const Color cadmiumViolet = Color(0xff8e3c95);
  static const Color vividTangerine = Color(0xfffa9d8f);
  static const Color spanishPink = Color(0xffffc2bf);
  static const Color salmon5 = Color(0xffF8836E);
  static const Color snow = Color(0xffFBF9F7);
  static const Color sunsetOrange = Color(0xffFF6752);
}

/** Remember
 * 100% — FF

    95% — F2

    90% — E6

    85% — D9

    80% — CC

    75% — BF

    70% — B3

    65% — A6

    60% — 99

    55% — 8C

    50% — 80

    45% — 73

    40% — 66

    35% — 59

    30% — 4D

    25% — 40

    20% — 33

    15% — 26

    10% — 1A

    5% — 0D

    0% — 00
 */
