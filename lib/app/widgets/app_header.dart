import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:quycky/app/widgets/custom_image.dart';
import 'package:quycky/core/utils/app_env.dart';
import 'package:quycky/core/utils/assets_strings.dart';
import 'package:sizer/sizer.dart';

enum LogoType { normal, full, welcome }

class AppHeader extends StatefulWidget {
  final String title;
  final String subtitle;
  final LogoType logo;
  final String? logoPath;
  final bool noTitleSubtitleSpace;
  final Widget? logoSectionRightWidget;
  final Widget? logoSectionLeftWidget;
  final double? topPadding;
  final Widget? centerChild;

  const AppHeader(
      {super.key,
      this.title = '',
      this.logo = LogoType.normal,
      this.logoPath,
      this.centerChild,
      this.subtitle = '',
      this.noTitleSubtitleSpace = false,
      this.logoSectionLeftWidget,
      this.logoSectionRightWidget,
      this.topPadding});

  @override
  State<AppHeader> createState() => _AppHeader();
}

class _AppHeader extends State<AppHeader> {
  bool isLessOrEqualsThanMinimumLayoutHeight = false;
  late final String logoPath;

  _updateLogo() {
    if (widget.logoPath == null) {
      switch (widget.logo) {
        case LogoType.welcome:
          logoPath = Assets.svg.quyckyWelcomeLogoWhite;
          break;
        case LogoType.normal:
          logoPath = Assets.svg.quyckyLogoWhiteDefault;
          break;
        default:
          logoPath = Assets.svg.quyckyLogoWhite;
          break;
      }
      // logoPath = widget.logo == LogoType.welcome
      //     ? Assets.svg.quyckyWelcomeLogoWhite
      //     : Assets.svg.quyckyLogoWhite;
      return;
    }
    logoPath = Assets.svg.quyckyLogoWhiteDefault;
  }

  @override
  initState() {
    _updateLogo();
    super.initState();
  }

  Widget getLogoSection() {
    final res = Stack(
      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
      // mainAxisSize: MainAxisSize.max,
      alignment: Alignment.center,
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: Container(
              constraints: BoxConstraints(maxHeight: 80),
              child: widget.logoSectionLeftWidget ?? SizedBox()),
        ),
        Align(
          alignment: Alignment.center,
          child: widget.centerChild != null
              ? widget.centerChild!
              : CustomImage(
                  logoPath,
                  width: (widget.logo == LogoType.full ||
                          widget.logo == LogoType.welcome)
                      ? 20.02.h //169
                      : 14.57.h,
                ),
        ),
        Align(
            alignment: Alignment.centerRight,
            child: SizedBox(
                height: 9.47.h,
                child: widget.logoSectionRightWidget ?? SizedBox())),
      ],
    );
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15), child: res);
  }

  Widget getSubtitle(String caption) => caption == ''
      ? Container()
      : Padding(
          padding: EdgeInsets.only(
              top: isLessOrEqualsThanMinimumLayoutHeight ? 8 : 14.0),
          child: Text(
            caption,
            style: const TextStyle(
                letterSpacing: 2,
                fontFamily: "Roboto",
                fontWeight: FontWeight.w600,
                color: Colors.white,
                fontSize: 10),
          ),
        );

  Widget getWidgetTitleAndSubtitle(String subtitle, String caption) =>
      widget.noTitleSubtitleSpace
          ? Container()
          : Column(
              children: [
                Text(
                  subtitle,
                  style: const TextStyle(
                      letterSpacing: 4,
                      fontFamily: "Roboto",
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                      fontSize: 12),
                ),
                getSubtitle(caption),
              ],
            );

  Widget getWidget() {
    return Padding(
      padding: EdgeInsets.only(
          top: widget.topPadding ??
              (!kIsWeb && Platform.isIOS
                  ? 0.0
                  : (isLessOrEqualsThanMinimumLayoutHeight
                      ? 10
                      : 20))), //45.0)),
      child: Column(
        children: [
          getLogoSection(),
          getWidgetTitleAndSubtitle(widget.title, widget.subtitle),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    isLessOrEqualsThanMinimumLayoutHeight =
        MediaQuery.of(context).size.height <= AppEnv.minimumLayoutHeight;
    return getWidget();
  }
}
