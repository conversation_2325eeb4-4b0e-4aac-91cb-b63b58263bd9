import 'package:flutter/material.dart';
import './confirmation_dialog.dart'; // Importa o ConfirmationDialog base

class BlockPlayerDialog extends StatelessWidget {
  final VoidCallback onDone;

  const BlockPlayerDialog({
    super.key,
    required this.onDone,
  });

  @override
  Widget build(BuildContext context) {
    return ConfirmationDialog(
      title: "PLAYER BLOCKED",
      message:
          "Player has been blocked and can no longer interact with you. To unblock, visit your account settings.",
      primaryActionLabel: "DONE",
      onPrimaryAction: () {
        onDone();
        // Fecha o diálogo ao concluir
        Navigator.of(context).pop();
      },
      // Não há botão secundário neste diálogo específico
    );
  }
}

/*
// Exemplo de como usar o BlockPlayerDialog:
//
// void _showPlayerBlockedConfirmation(BuildContext context) {
//   showDialog(
//     context: context,
//     barrierDismissible: false,
//     builder: (BuildContext dialogContext) {
//       return BlockPlayerDialog(
//         onDone: () {
//           print("Confirmação de bloqueio concluída.");
//         },
//       );
//     },
//   );
// }
*/
