import 'package:flutter/material.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:sizer/sizer.dart';

class ConfirmationDialog extends StatelessWidget {
  final String title;
  final String message;
  final String? primaryActionLabel;
  final VoidCallback? onPrimaryAction;
  final String? secondaryActionLabel;
  final VoidCallback? onSecondaryAction;

  const ConfirmationDialog({
    super.key,
    required this.title,
    required this.message,
    this.primaryActionLabel,
    this.onPrimaryAction,
    this.secondaryActionLabel,
    this.onSecondaryAction,
  });

  @override
  Widget build(BuildContext context) {
    List<Widget> buttons = [];

    if (primaryActionLabel != null && onPrimaryAction != null) {
      buttons.add(
        SizedBox(
          width: 67.43.w,
          height: 6.05.h,
          child: Button(
            autoSized: true,
            text: primaryActionLabel!,
            onPressed: onPrimaryAction,
          ),
        ),
      );
    }

    if (secondaryActionLabel != null && onSecondaryAction != null) {
      if (buttons.isNotEmpty) {
        buttons.add(const SizedBox(height: 12)); // Espaço entre os botões
      }
      buttons.add(
        SizedBox(
          width: 67.43.w,
          height: 6.05.h,
          child: Button(
            autoSized: true,
            onPressed: onSecondaryAction,
            text: secondaryActionLabel!,
            outlined: true,
            borderColor: CustomColors.primary,
            textColor: CustomColors.primary,
          ),
        ),
      );
    }

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius:
            BorderRadius.circular(16.0), // Cantos arredondados do dialog
      ),
      elevation: 0,
      backgroundColor:
          Colors.transparent, // Para o Container interno controlar o fundo
      child: Container(
        padding: const EdgeInsets.all(24.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Column(
          mainAxisSize:
              MainAxisSize.min, // Para o dialog se ajustar ao conteúdo
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Text(
              title.toUpperCase(),
              textAlign: TextAlign.center,
              style: TextStyle(
                color: CustomColors.primary,
                fontSize: 16,
                letterSpacing: 0.2,
                fontWeight: FontWeight.w800,
              ),
            ),
            SizedBox(height: 2.95.h),
            Text(
              message,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: CustomColors.primary,
                fontSize: 14,
                fontWeight: FontWeight.w400,
                height: 1.2,
                letterSpacing: 0.2,
              ),
            ),
            SizedBox(height: 2.95.h),
            if (buttons.isNotEmpty)
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment
                    .stretch, // Para os botões ocuparem a largura
                children: buttons,
              ),
          ],
        ),
      ),
    );
  }
}

/*
// Exemplo de como usar o ConfirmationDialog:
//
// void _showMyCustomDialog(BuildContext context) {
//   showDialog(
//     context: context,
//     barrierDismissible: false, // Opcional: impede fechar ao tocar fora
//     builder: (BuildContext dialogContext) {
//       return ConfirmationDialog(
//         title: "Report Submitted",
//         message: "This user has been successfully reported. We monitor reports from multiple players to ensure community safety.",
//         primaryActionLabel: "Block Player",
//         onPrimaryAction: () {
//           print("Ação primária: Bloquear jogador");
//           Navigator.of(dialogContext).pop(); // Fecha o dialog
//         },
//         secondaryActionLabel: "Return",
//         onSecondaryAction: () {
//           print("Ação secundária: Retornar");
//           Navigator.of(dialogContext).pop(); // Fecha o dialog
//         },
//       );
//     },
//   );
// }

// void _showMyCustomDialogSingleButton(BuildContext context) {
//   showDialog(
//     context: context,
//     barrierDismissible: false,
//     builder: (BuildContext dialogContext) {
//       return ConfirmationDialog(
//         title: "Player Blocked",
//         message: "Player has been blocked and can no longer interact with you. To unblock, visit your account settings.",
//         primaryActionLabel: "Done",
//         onPrimaryAction: () {
//           print("Ação: Concluído");
//           Navigator.of(dialogContext).pop();
//         },
//       );
//     },
//   );
// }
*/
