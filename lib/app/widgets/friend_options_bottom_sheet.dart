import 'package:flutter/material.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/button.dart';
// import 'package:quycky/app/widgets/report_player_dialog.dart'; // Removido
// import 'package:quycky/app/widgets/block_player_dialog.dart'; // Removido
import 'package:sizer/sizer.dart';

class FriendOptionsBottomSheet extends StatelessWidget {
  final VoidCallback? doInviteToPlay;
  final VoidCallback? doEraseChatHistory;
  final VoidCallback? doUnmatchPlayer;
  final VoidCallback? doReportPlayer;
  final VoidCallback? doBlockPlayer;

  const FriendOptionsBottomSheet({
    super.key,
    this.doInviteToPlay,
    this.doEraseChatHistory,
    this.doUnmatchPlayer,
    this.doReportPlayer,
    this.doBlockPlayer,
  });

  // Removidos os métodos _showReportPlayerDialog e _showBlockPlayerDialog

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade600,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, -5), // changes position of shadow
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Container(
              width: 12.w,
              height: 0.6.h,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(10.0),
              ),
            ),
            SizedBox(height: 3.h),
            _buildOptionButton(
              text: 'INVITE TO PLAY',
              onPressed: () {
                Navigator.pop(context);
                doInviteToPlay?.call();
              },
              textColor: CustomColors.primary,
              backgroundColor: Colors.white,
            ),
            SizedBox(height: 1.5.h),
            _buildOptionButton(
              text: 'ERASE CHAT HISTORY',
              isOutlined: true,
              onPressed: () {
                Navigator.pop(context);
                doEraseChatHistory?.call();
              },
            ),
            SizedBox(height: 1.5.h),
            _buildOptionButton(
              text: 'UNMATCH PLAYER',
              isOutlined: true,
              onPressed: () {
                Navigator.pop(context);
                doUnmatchPlayer?.call();
              },
            ),
            SizedBox(height: 1.5.h),
            _buildOptionButton(
              text: 'REPORT PLAYER',
              isOutlined: true,
              onPressed: () {
                Navigator.pop(context);
                doReportPlayer?.call();
              },
            ),
            SizedBox(height: 1.5.h),
            _buildOptionButton(
              text: 'BLOCK PLAYER',
              isOutlined: true,
              onPressed: () {
                Navigator.pop(context);
                doBlockPlayer?.call();
              },
            ),
            SizedBox(height: 2.h), // Espaço extra na parte inferior
          ],
        ),
      ),
    );
  }

  Widget _buildOptionButton({
    required String text,
    required VoidCallback onPressed,
    Color backgroundColor = Colors.transparent,
    Color? textColor,
    bool isOutlined = false,
  }) {
    return SizedBox(
        width: 87.18.w,
        height: 5.8.h,
        child: Button(
          autoSized: true,
          text: text,
          onPressed: onPressed,
          borderColor: Colors.white,
          outlined: isOutlined,
          color: backgroundColor,
          textColor: textColor ?? Colors.white,
        ));
  }
}
