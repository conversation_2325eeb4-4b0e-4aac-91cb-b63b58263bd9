class AppRoutes {
  static String initialRoute = '/';
  static String terms = '/terms';
  static String tutorial = '/tutorial';
  static String home = '/home/';
  // User
  static String user = '/user';
  static String userRegister({complete = true}) =>
      '${complete ? user : ''}/register';
  static String userProfile({complete = true}) =>
      '${complete ? user : ''}/profile';
  // End User
  static String userFriendProfile({int? id}) =>
      '/user/friends/profile/${id ?? ':id'}';
  static String userFriendsList = '/user/friends/list';
  static String checkFriendMatch = '/user/friends/match';
  static String profileQrCodeShare = '/user/friends/share/qr_code';
  // Game module pages
  static String game = '/game';
  static String gameLobby({complete = true}) => '${complete ? game : ''}/lobby';
  static String gamePlay({complete = true}) => '${complete ? game : ''}/play';
  static String gameEnd({complete = true}) => '${complete ? game : ''}/end';
  static String readGameQrCode({complete = true}) =>
      '${complete ? game : ''}/read/qr_code';
}
