/// Flutter icons QuyckyIcons
/// Copyright (C) 2025 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  QuyckyIcons
///      fonts:
///       - asset: fonts/QuyckyIcons.ttf
///
///
/// * Material Design Icons, Copyright (C) Google, Inc
///         Author:    Google
///         License:   Apache 2.0 (https://www.apache.org/licenses/LICENSE-2.0)
///         Homepage:  https://design.google.com/icons/
/// * Typicons, (c) <PERSON> 2012
///         Author:    <PERSON>
///         License:   SIL (http://scripts.sil.org/OFL)
///         Homepage:  http://typicons.com/
/// * Linecons, Copyright (C) 2013 by Designmodo
///         Author:    Designmodo for Smashing Magazine
///         License:   CC BY ()
///         Homepage:  http://designmodo.com/linecons-free/
/// * Font Awesome 4, Copyright (C) 2016 by <PERSON>
///         Author:    <PERSON>
///         License:   SIL ()
///         Homepage:  http://fortawesome.github.com/Font-Awesome/
/// * Font Awesome 5, Copyright (C) 2016 by Dave Gandy
///         Author:    Dave Gandy
///         License:   SIL (https://github.com/FortAwesome/Font-Awesome/blob/master/LICENSE.txt)
///         Homepage:  http://fortawesome.github.com/Font-Awesome/
/// * Linearicons Free, Copyright (C) Linearicons.com
///         Author:    Perxis
///         License:   CC BY-SA 4.0 (https://creativecommons.org/licenses/by-sa/4.0/)
///         Homepage:  https://linearicons.com
/// * RPG Awesome, Copyright (c) 2014, Daniela Howe
///         Author:    Daniela Howe & Ivan Montiel
///         License:   SIL (http://scripts.sil.org/OFL)
///         Homepage:  http://nagoshiashumari.github.io/Rpg-Awesome/
///
import 'package:flutter/widgets.dart';

class QuyckyIcons {
  QuyckyIcons._();

  static const _kFontFam = 'QuyckyIcons';
  static const String? _kFontPkg = null;

  static const IconData add =
      IconData(0xe800, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData cancel_circled_outline =
      IconData(0xe801, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData gear =
      IconData(0xe802, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData user_add_outline =
      IconData(0xe803, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData user_add =
      IconData(0xe804, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData person_add =
      IconData(0xe805, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData iq =
      IconData(0xe806, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData params =
      IconData(0xe807, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData add_a_photo =
      IconData(0xe808, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData access_alarm =
      IconData(0xe809, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData access_time =
      IconData(0xe80a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData access_alarms =
      IconData(0xe80b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData image =
      IconData(0xe80c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData picture =
      IconData(0xe80d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData th_large =
      IconData(0xe80e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData th =
      IconData(0xe80f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData file_image =
      IconData(0xe810, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData question_circle =
      IconData(0xe811, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData heart =
      IconData(0xe812, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData close_circle =
      IconData(0xe813, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData person_circle =
      IconData(0xe814, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData arrow_top_circle =
      IconData(0xe815, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData arrow_right_circle =
      IconData(0xe816, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData arrow_left_circle =
      IconData(0xe817, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData arrow_down_circle =
      IconData(0xe818, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData heart_2 =
      IconData(0xe819, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData friends =
      IconData(0xe81a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData arrow_left =
      IconData(0xe81b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData arrow_right =
      IconData(0xe81c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData add_1 =
      IconData(0xe81d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData file_add =
      IconData(0xe81e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData arrow_down_rounded =
      IconData(0xe81f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData user =
      IconData(0xe820, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData cup =
      IconData(0xe821, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData cup_outlined =
      IconData(0xe822, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData close_circle_2 =
      IconData(0xe823, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData add_circle =
      IconData(0xe824, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData search =
      IconData(0xe825, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData gift_icon =
      IconData(0xe826, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData media_play =
      IconData(0xe827, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData users =
      IconData(0xe828, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData person_2 =
      IconData(0xe829, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData people =
      IconData(0xe82a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData heart_search =
      IconData(0xe82b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData people_outlined =
      IconData(0xe82c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData read_qr_code =
      IconData(0xe82d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData copy =
      IconData(0xe82e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData send =
      IconData(0xe82f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData gear_heart =
      IconData(0xe9dc, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData image_1 =
      IconData(0xf03e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData file_image_2 =
      IconData(0xf1c5, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData user_plus =
      IconData(0xf234, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData images =
      IconData(0xf302, fontFamily: _kFontFam, fontPackage: _kFontPkg);
}
